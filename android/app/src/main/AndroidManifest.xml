<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"
        />
    <uses-permission
        android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
    </queries>

    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:supportsRtl="true"
        android:requestLegacyExternalStorage="true"
        android:extractNativeLibs="true"
        android:zygotePreloadName="vkey.android.vos.AppZygote"
        android:theme="@style/AppTheme">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
      </activity>
        <activity
            android:name="com.vkey.android.support.permission.VGuardPermissionActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            tools:replace="android:theme"/>
        <service
            android:name="com.vkey.android.secure.overlay.OverlayService" />
        <service
            android:name="vkey.android.vos.MgService"
            android:enabled="true"/>
        <service android:name="com.vkey.android.internal.vguard.cache.ProcessHttpRequestIntentService"
        android:permission="android.permission.BIND_JOB_SERVICE" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" />
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />
    </application>
</manifest>
