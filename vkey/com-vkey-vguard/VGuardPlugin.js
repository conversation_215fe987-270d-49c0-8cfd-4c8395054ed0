import { NativeModules, NativeEventEmitter, Platform } from 'react-native';

const { VGuardPlugin } = NativeModules;

// Note: Event listening should be done in your component, not at module level
// Example usage in your component:
// if(Platform.OS === 'ios' && VGuardPlugin){
//     const myModuleEvt = new NativeEventEmitter(VGuardPlugin)
//     myModuleEvt.addListener(VGuardPlugin.VGUARD_EVENTS, (data) => console.log(data))
// }

export default VGuardPlugin