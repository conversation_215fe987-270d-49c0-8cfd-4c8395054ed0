PODS:
  - boost (1.76.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.4)
  - FBReactNativeSpec (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.4)
    - RCTTypeSafety (= 0.72.4)
    - React-Core (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.72.4):
    - hermes-engine/Pre-built (= 0.72.4)
  - hermes-engine/Pre-built (0.72.4)
  - libevent (2.1.12)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.4)
  - RCTTypeSafety (0.72.4):
    - FBLazyVector (= 0.72.4)
    - RCTRequired (= 0.72.4)
    - React-Core (= 0.72.4)
  - React (0.72.4):
    - React-Core (= 0.72.4)
    - React-Core/DevSupport (= 0.72.4)
    - React-Core/RCTWebSocket (= 0.72.4)
    - React-RCTActionSheet (= 0.72.4)
    - React-RCTAnimation (= 0.72.4)
    - React-RCTBlob (= 0.72.4)
    - React-RCTImage (= 0.72.4)
    - React-RCTLinking (= 0.72.4)
    - React-RCTNetwork (= 0.72.4)
    - React-RCTSettings (= 0.72.4)
    - React-RCTText (= 0.72.4)
    - React-RCTVibration (= 0.72.4)
  - React-callinvoker (0.72.4)
  - React-Codegen (0.72.4):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.4)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.4)
    - React-Core/RCTWebSocket (= 0.72.4)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.4)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.4)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/CoreModulesHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-RCTBlob
    - React-RCTImage (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.4):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.4)
    - React-debug (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-jsinspector (= 0.72.4)
    - React-logger (= 0.72.4)
    - React-perflogger (= 0.72.4)
    - React-runtimeexecutor (= 0.72.4)
  - React-debug (0.72.4)
  - React-hermes (0.72.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.4)
    - React-jsi
    - React-jsiexecutor (= 0.72.4)
    - React-jsinspector (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - React-jsi (0.72.4):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - React-jsinspector (0.72.4)
  - React-logger (0.72.4):
    - glog
  - react-native-vguard (2.0.0):
    - React
  - React-NativeModulesApple (0.72.4):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.4)
  - React-RCTActionSheet (0.72.4):
    - React-Core/RCTActionSheetHeaders (= 0.72.4)
  - React-RCTAnimation (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTAnimationHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTAppDelegate (0.72.4):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.4):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTBlobHeaders (= 0.72.4)
    - React-Core/RCTWebSocket (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-RCTNetwork (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTImage (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTImageHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-RCTNetwork (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTLinking (0.72.4):
    - React-Codegen (= 0.72.4)
    - React-Core/RCTLinkingHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTNetwork (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTNetworkHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTSettings (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.4)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTSettingsHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-RCTText (0.72.4):
    - React-Core/RCTTextHeaders (= 0.72.4)
  - React-RCTVibration (0.72.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.4)
    - React-Core/RCTVibrationHeaders (= 0.72.4)
    - React-jsi (= 0.72.4)
    - ReactCommon/turbomodule/core (= 0.72.4)
  - React-rncore (0.72.4)
  - React-runtimeexecutor (0.72.4):
    - React-jsi (= 0.72.4)
  - React-runtimescheduler (0.72.4):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.4):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.4)
    - React-cxxreact (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-logger (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - ReactCommon/turbomodule/core (0.72.4):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.4)
    - React-cxxreact (= 0.72.4)
    - React-jsi (= 0.72.4)
    - React-logger (= 0.72.4)
    - React-perflogger (= 0.72.4)
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-vguard (from `../node_modules/react-native-vguard`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - fmt
    - libevent
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-08-07-RNv0.72.4-813b2def12bc9df02654b3e3653ae4a68d0572e0
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-vguard:
    :path: "../node_modules/react-native-vguard"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 5d4a3b7f411219a45a6d952f77d2c0a6c9989da5
  FBReactNativeSpec: 3fc2d478e1c4b08276f9dd9128f80ec6d5d85c1f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 81191603c4eaa01f5e4ae5737a9efcf64756c7b2
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: c0569ecc035894e4a68baecb30fe6a7ea6e399f9
  RCTTypeSafety: e90354072c21236e0bcf1699011e39acd25fea2f
  React: a1be3c6dc0a6e949ccd3e659781aa47bbae1868f
  React-callinvoker: 1020b33f6cb1a1824f9ca2a86609fbce2a73c6ed
  React-Codegen: a0a26badf098d4a779acda922caf74f6ecabed28
  React-Core: 52075b80f10c26f62219d7b5d13d7d8089f027b3
  React-CoreModules: 21abab85d7ad9038ce2b1c33d39e3baaf7dc9244
  React-cxxreact: 4ad1cc861e32fb533dad6ff7a4ea25680fa1c994
  React-debug: 17366a3d5c5d2f5fc04f09101a4af38cb42b54ae
  React-hermes: 37377d0a56aa0cf55c65248271866ce3268cde3f
  React-jsi: 6de8b0ccc6b765b58e4eee9ee38049dbeaf5c221
  React-jsiexecutor: c7f826e40fa9cab5d37cab6130b1af237332b594
  React-jsinspector: aaed4cf551c4a1c98092436518c2d267b13a673f
  React-logger: da1ebe05ae06eb6db4b162202faeafac4b435e77
  react-native-vguard: 7903e1fdd6eecafc0c2f638d7681b8c27a7fe384
  React-NativeModulesApple: edb5ace14f73f4969df6e7b1f3e41bef0012740f
  React-perflogger: 496a1a3dc6737f964107cb3ddae7f9e265ddda58
  React-RCTActionSheet: 02904b932b50e680f4e26e7a686b33ebf7ef3c00
  React-RCTAnimation: 88feaf0a85648fb8fd497ce749829774910276d6
  React-RCTAppDelegate: 5792ac0f0feccb584765fdd7aa81ea320c4d9b0b
  React-RCTBlob: 0dbc9e2a13d241b37d46b53e54630cbad1f0e141
  React-RCTImage: b111645ab901f8e59fc68fbe31f5731bdbeef087
  React-RCTLinking: 3d719727b4c098aad3588aa3559361ee0579f5de
  React-RCTNetwork: b44d3580be05d74556ba4efbf53570f17e38f734
  React-RCTSettings: c0c54b330442c29874cd4dae6e94190dc11a6f6f
  React-RCTText: 9b9f5589d9b649d7246c3f336e116496df28cfe6
  React-RCTVibration: 691c67f3beaf1d084ceed5eb5c1dddd9afa8591e
  React-rncore: 142268f6c92e296dc079aadda3fade778562f9e4
  React-runtimeexecutor: d465ba0c47ef3ed8281143f59605cacc2244d5c7
  React-runtimescheduler: 4941cc1b3cf08b792fbf666342c9fc95f1969035
  React-utils: b79f2411931f9d3ea5781404dcbb2fa8a837e13a
  ReactCommon: 4b2bdcb50a3543e1c2b2849ad44533686610826d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 3efc43e0d48686ce2e8c60f99d4e6bd349aff981

PODFILE CHECKSUM: 34e0fd4a6223cb3d0f8b00278e9a847cb9f287f1

COCOAPODS: 1.15.2
