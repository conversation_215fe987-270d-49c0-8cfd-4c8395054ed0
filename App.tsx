/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useRef, useEffect, useState } from 'react';
import type { PropsWithChildren } from 'react';
import {
  DeviceEventEmitter,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Button,
  Text,
  useColorScheme,
  View,
  Alert, TextInput,
  AppState
} from 'react-native';

import {
  Colors,
} from 'react-native/Libraries/NewAppScreen';

//@ts-ignore
import { VGuardPlugin } from 'react-native-vguard';

type SectionProps = PropsWithChildren<{
  title: string;
}>;

function Section({ children, title }: SectionProps): JSX.Element {

  return (
    <View style={styles.sectionContainer}>
      <Text
        style={[
          styles.ButtonStyle,
        ]}>
        {children}
      </Text>
    </View>
  );
}

function App(): JSX.Element {
  const [logs, setLogs] = useState('');
  const [logItem, setLogItem] = useState('');
  const [payload, onChangePayload] = useState('message to sign');

  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);

  const getData = async () => {
    try {
      // Construct the API URL
      const url = `https://api.restful-api.dev/objects`;
  
      // Fetch the data
      const response = await fetch(url);
  
      // Check if the response status is OK (200 range)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
  
      // Parse the JSON response
      const result = await response.json();
  
      // Introduce a 30-second delay
      await new Promise(resolve => setTimeout(resolve, 30000));
  
      // Return the data
      return {
        result
      };
    } catch (error) {
      // Handle errors (e.g., network issues or invalid city)
      console.error('Error fetching data:', error);
      return null;
    }
  };

  const getData3 = async () => {
    try {
      const response = await fetch(
        'https://apiapp.acbtest.vn/sit2/mba/configuration/setting/feature-by-group',
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'apikey': '2DexvlG3bB9VT6SuoRaW6UzFzNKWC55y',
            'x-app-version': '3.30.1',
            'cache-control': 'no-cache',
            'x-conversation-id': '08388aca-67063203-492c-bcda-eb3136fa931e-1-',
            'x-device-id': 'D480120E-4694-45AA-A3B8-3CDF281AD6C0',
            'x-request-id': '25eac276-81d3-4f36-8fbc-4a1897cb4b80',
            'accept-language': 'vi',
          },
        }
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Response Data:', data);
      return data;
    } catch (error) {
      console.error('Error fetching data:', error);
      return null;
    }
  };

  const getData2 = async () => {
    try {
      // Construct the API URL
      const url = `https://mobile.useinsider.com/api/v3/session/stop`;
  
      // Fetch the data
      const response = await fetch(url);
  
      // Check if the response status is OK (200 range)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
  
      // Parse the JSON response
      const result = await response.json();
  
      // Introduce a 30-second delay
      await new Promise(resolve => setTimeout(resolve, 30000));
  
      // Return the data
      return {
        result
      };
    } catch (error) {
      // Handle errors (e.g., network issues or invalid city)
      console.error('Error fetching data:', error);
      return null;
    }
  };

  const printLogs = (log: string) => {
    setLogItem(log);
    console.log(log);
  }

  useEffect(() => {
    // This effect will run after each logItem update
    const value = logs + "\n\n" + logItem;
    setLogs(value);
    console.log('logs::' + logs);
  }, [logItem]);

  useEffect(() => {
    // getData3().then((result) => {
    //   printLogs('result from API:' + JSON.stringify(result));
    //   console.log('result from API:' + JSON.stringify(result));
    // })
    DeviceEventEmitter.addListener(VGuardPlugin.VGUARD_EVENTS, onVGuardEvents);
    startVGuard();
    return () => {
      // Remove the event listener before the component is destroyed.
      DeviceEventEmitter.removeAllListeners(VGuardPlugin.VGUARD_EVENTS);
    };
  }, []);

  useEffect(() => {
    const subscription = AppState.addEventListener("change", nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === "active"
      ) {
        console.log("App has come to the foreground!");
        startVGuard();
      }

      appState.current = nextAppState;
      setAppStateVisible(appState.current);
      console.log("AppState", appState.current);
    });

    return () => {
      subscription.remove();
    };
  }, []);

  const startVGuard = () => {
    printLogs('START VGUARD');
    // Set url of synchronizing the vos logs if enabled
    const tlaUrl = 'https://stg-cloud.v-key.com';
    if (tlaUrl) {
      VGuardPlugin.setLoggerBaseUrl(tlaUrl);
    }
    // Set TI Url if enabled
    const tiUrl = 'https://stg-cloud.v-key.com/';
    if (tiUrl) {
      VGuardPlugin.setThreatIntelligenceServerURL(tiUrl);
    }
    // 0: DEFAULT, 1: HIGHT
    VGuardPlugin.setMemoryConfiguration(1);
    // Enable the overlay detection
    if (Platform.OS === 'android') {
      VGuardPlugin.setOverlayDetectionEnabled(false);
    }

    // Set TA Alias
    let TA_alias = "ACB_Corp";
    VGuardPlugin.setTaAlias(TA_alias);
    printLogs('setTaAlias ' + TA_alias);
    // Intialize Vguard
    VGuardPlugin.setupVGuard();
  };

  const onVGuardEvents = async (event: any) => {
    const action = event.action;
    printLogs('. event.action: ' + event.action);

    if (action == VGuardPlugin.ACTION_SCAN_COMPLETE) {
      const threats = event.data;
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          // printLogs(
          //   'Threat Info: ' +
          //     threatInfo.ThreatClass +
          //     ' - ' +
          //     threatInfo.ThreatName +
          //     ' - ' +
          //     threatInfo.ThreatInfo +
          //     ' - ' +
          //     threatInfo.ThreatPackageID,
          // );
        }
      } else {
        printLogs('Scan complete, no threats found!');
      }
    } else if (action == VGuardPlugin.VOS_READY) {
      printLogs('v-os return code: ' + event.data);

      const errorCode = event.data;
      if (errorCode == '-1039' || errorCode == '20050') {// iOS behavior
        // showWarningAlert('Simulator is detected: ' + errorCode)
      }

      const tid = await VGuardPlugin.getTroubleshootingId();
      printLogs('TID: ' + tid);

    } else if (action == VGuardPlugin.VGUARD_ERROR) {
      // Force synce to send TLA logs if vguard initialization fails
      // VGuardPlugin.forceSyncLogs(); // called automatically in Plugin already

      const errorCode = event.data;
      if (errorCode == '-1039' || errorCode == '20050') {// Android behavior
        showWarningAlert('Emulator is detected: ' + errorCode)
      } else {
        printLogs('Initialize V-Guard is failure caused: ' + errorCode);
      }

    } else if (action == VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) { // This event is for Android only
      showWarningAlert('app is running on Virtual Space')
    } else if (action == VGuardPlugin.VGUARD_OVERLAY_DETECTED) { // This event is for Android only
      showWarningAlert('OVERLAY DETECTED!')
    } else if (action == VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
      if (Platform.OS === 'android') {
        var _data = 'VGUARD_SCREEN_SHARING_DETECTED'
        const data = event.data;
        if (data != null && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            _data += '\nScreen Name: ' + data[i];
          }
        }
        showWarningAlert('OVERLAY DETECTED!')
      } else if (Platform.OS === 'ios') {
        showWarningAlert('SCREEN_SHARING DETECTED!')
      }

    } else if (action == VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY) {
      const data = event.data;
      // printLogs("data: " + data);
      // printLogs('highest_policy: ' + data.highest_policy);
      // printLogs('alertTitle: ' + data.alertTitle);
      // printLogs('alertMessage: ' + data.alertMessage);
      // printLogs('disabledAppExpired: ' + data.disabledAppExpired);
      
      const threats = data.threats;
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          // printLogs(
          //   'Threat Info: ' +
          //   threatInfo.ThreatClass +
          //   ' - ' +
          //   threatInfo.ThreatName +
          //   ' - ' +
          //   threatInfo.ThreatInfo +
          //   ' - ' +
          //   threatInfo.ThreatPackageID,
          // );
        }
      } else {
        printLogs('VGUARD_HANDLE_THREAT_POLICY, no threats found!');
      }
    } else {
      printLogs('. event.data: ' + event.data);
    }
  };

  const signMsg = async () => {
    printLogs('trigger cryptoTA.signMsg');
    printLogs("--------");
    printLogs("payload:\n" + payload);
    const signature = await VGuardPlugin.signMsg(payload)
    printLogs("==>");
    printLogs("signature:\n" + signature);
    printLogs("--------");
  }

  const signMsgChecksum = async () => {
    printLogs("trigger cryptoTA.signMsgWithChecksum")
    printLogs("--------");
    printLogs("payload:\n" + payload);

    const signatureData = await VGuardPlugin.signMsgChecksum(payload)
    printLogs("==>");
    if (signatureData) {
      // printLogs("signatureData:\n" + signatureData);

      //Handle signature data
      const signatureDataObject = JSON.parse(signatureData)
      const numberOfKeys = Object.keys(signatureDataObject).length
      printLogs("Number of keys: " + numberOfKeys)
      switch (numberOfKeys) {
        case 0: {//{}
          printLogs("JSONException happened!")
          break
        }
        case 1: {//{error:XXX}
          printLogs("Error: " + signatureDataObject.error)
          // get Pri error and Sec error

          break
        }
        case 2: {//{primaryError:XXX, SecondaryError:XXX}
          printLogs("primaryError: " + signatureDataObject.primaryError)
          printLogs("SecondaryError: " + signatureDataObject.SecondaryError)
          break
        }// new add by tu.do
        case 3: {//{signature:XXX, checksum:XXX, epoctime:XXX}
          //Get base64 encoded data
          const signature = signatureDataObject.signature
          printLogs("signature: " + signature)
          const checksum = signatureDataObject.checksum
          printLogs("checksum: " + checksum)
          const epoctime = signatureDataObject.epoctime
          printLogs("epoctime: " + epoctime)
          break
        }
        default: {//2
          printLogs("result signMsgWithChecksum: " + signatureDataObject)
          break
        }
      }
    }
    printLogs("--------");
  }

  const showWarningAlert = (message: string) => {
    printLogs(message);

    Alert.alert('Warning', message, [
      { text: 'OK', onPress: () => "" },
    ]);
  }

  const isDarkMode = useColorScheme() === 'dark';

  const SafeAreaViewStyle = {
    backgroundColor: Colors.white,
  };
  const scrollViewStyle = {
    height: 300,
    backgroundColor: 'lightgray',
    borderRadius: 5,
    margin: 10,
  };
  const logStyle = {
    color: 'black',
  };

  return (
    <SafeAreaView style={SafeAreaViewStyle}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
      />
      <View style={styles.sectionContainer}>
        <TextInput
          style={styles.input}
          placeholder="Type here to sign!"
          onChangeText={onChangePayload}
          value={payload}
        />
      </View>
      <Section title=''>
        <Button
          onPress={() => {
            printLogs('trigger request scan');
            VGuardPlugin.requestScan();
          }}
          style={{ backgroundColor: 'red', Colors: 'red', TextColor: 'red' }}
          title="scan"
          color="#841584" />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => {
            printLogs('trigger forceSyncLogs');
            VGuardPlugin.forceSyncLogs();
          }}
          title="forceSyncLogs"
          color="#841584" />
        <View style={{ padding: 2 }} />
        <Button
          onPress={() => {
            signMsg()
          }}
          title="signMsg"
          color="#841584" />
        <View style={{ padding: 2 }} />
        <Button
          onPress={() => { signMsgChecksum() }}
          title="signMsgChecksum"
          color="#841584" />
        <Button
          onPress={() => { VGuardPlugin.showPopupVkey('', '', true, 5) }}
          title="show popup quit 1s"
          color="#841584" />
        <Button
          onPress={() => { VGuardPlugin.showPopupVkey('Ứng dụng mà Bạn đang cài đặt trên thiết bị này không an toàn', 'Để bảo vệ an toàn cho Bạn, ứng dụng ACB ONE sẽ không hoạt động trên các thiết bị có dấu hiệu bị xâm nhập hoặc có nguy cơ bị đánh cắm thông tin.', false, 5) }}
          title="show popup no quit 1s"
          color="#841584" />

      </Section>
      <Section title="Logs" />
      <ScrollView style={scrollViewStyle}>
        <View style={styles.sectionContainer}>
          <Text style={logStyle}> {logs} </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 10,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  ButtonStyle: {
    backgroundColor: Colors.lighter,
  },
  highlight: {
    fontWeight: '700',
  },
  input: {
    height: 40,
    width: 330,
    margin: 12,
    borderWidth: 1,
    padding: 10,
    backgroundColor: Colors.lighter,
    color: Colors.black
  }
});

export default App;